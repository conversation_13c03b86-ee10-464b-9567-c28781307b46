{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "cli": {"analytics": false, "schematicCollections": ["angular-eslint"]}, "projects": {"desktop": {"projectType": "application", "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": "dist", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "styles": ["src/styles.scss"], "assets": ["src/assets"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1000kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "200kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "options": {"port": 9245}, "configurations": {"production": {"buildTarget": "desktop:build:production"}, "development": {"buildTarget": "desktop:build:development"}}, "defaultConfiguration": "development"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "schematics": {"@schematics/angular:component": {"type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}}