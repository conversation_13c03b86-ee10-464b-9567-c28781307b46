version: "3"

includes:
  darwin: ./desktop/build/darwin/Taskfile.yml

vars:
  APP_NAME: "ns-drive"
  BIN_DIR: "bin"
  VITE_PORT: "{{.WAILS_VITE_PORT | default 9245}}"

tasks:
  build:
    summary: Builds the application
    dir: desktop
    cmds:
      - task: "{{OS}}:build"

  package:
    summary: Packages a production build of the application
    dir: desktop
    cmds:
      - task: "{{OS}}:package"

  run:
    summary: Runs the application
    dir: desktop
    cmds:
      - task: "{{OS}}:run"

  dev:frontend:
    summary: Starts the frontend development server
    dir: desktop/frontend
    cmds:
      - yarn start --port {{.VITE_PORT}}

  dev:
    summary: Runs the application in development mode (requires frontend dev server to be running)
    dir: desktop
    cmds:
      - '{{env "GOPATH"}}/bin/wails3 dev -config ./build/config.yml -port {{.VITE_PORT}}'
